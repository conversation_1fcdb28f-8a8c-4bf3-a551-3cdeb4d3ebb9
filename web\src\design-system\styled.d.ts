import 'styled-components';
import { lightColors } from './tokens/colors';
import { typography } from './tokens/typography';
import { spacing } from './tokens/spacing';
import { shadows } from './tokens/shadows';
import { radius } from './tokens/radius';

declare module 'styled-components' {
  export interface DefaultTheme {
    colors: typeof lightColors;
    typography: typeof typography;
    spacing: typeof spacing;
    shadows: typeof shadows;
    radius: typeof radius;
  }
}