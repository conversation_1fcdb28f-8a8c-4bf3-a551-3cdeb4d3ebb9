import 'styled-components';
import { Colors } from './tokens/colors';
import { Typography } from './tokens/typography';
import { Spacing } from './tokens/spacing';
import { Shadows } from './tokens/shadows';
import { Radius } from './tokens/radius';

declare module 'styled-components' {
  export interface DefaultTheme {
    colors: Colors;
    typography: Typography;
    spacing: Spacing;
    shadows: Shadows;
    radius: Radius;
  }
} 