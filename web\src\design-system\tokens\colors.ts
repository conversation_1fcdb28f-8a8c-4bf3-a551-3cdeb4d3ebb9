// 向后兼容的简化颜色系统 - 快速修复版本
export const lightColors = {
  primary: '#3182CE',
  secondary: '#ED8936',
  tertiary: '#63B3ED',
  quaternary: '#FFCC00',
  background: '#FFFFFF',
  surface: '#F7FAFC',
  text: '#1A202C',
  textSecondary: '#718096',
  separator: '#E2E8F0',
  red: '#E53E3E',
  green: '#38A169',
  blue: '#3182CE',
  yellow: '#D69E2E',
  orange: '#ED8936',
  purple: '#805AD5',
  pink: '#D53F8C',
  teal: '#319795',
  indigo: '#5A67D8',
  brown: '#A0792C',
  gray: '#718096',
  // 向后兼容的属性
  separator: '#E2E8F0',
  textSecondary: '#718096',
  surface: '#F7FAFC',
};

export const darkColors = {
  primary: '#4299E1',
  secondary: '#F6AD55',
  tertiary: '#90CDF4',
  quaternary: '#F6E05E',
  background: '#1A202C',
  surface: '#2D3748',
  text: '#FFFFFF',
  textSecondary: '#A0AEC0',
  separator: '#4A5568',
  red: '#F56565',
  green: '#68D391',
  blue: '#4299E1',
  yellow: '#F6E05E',
  orange: '#F6AD55',
  purple: '#B794F6',
  pink: '#F687B3',
  teal: '#4FD1C7',
  indigo: '#7C3AED',
  brown: '#D69E2E',
  gray: '#A0AEC0',
  // 向后兼容的属性
  separator: '#4A5568',
  textSecondary: '#A0AEC0',
  surface: '#2D3748',
};

// 向后兼容的颜色别名
export const Colors = {
  lightColors,
  darkColors,
};

export type Colors = typeof lightColors; 