// 基础色彩调色板 - 提供完整的色彩层级
const colorPalette = {
  // 主色调 - 蓝色系
  blue: {
    50: '#EBF8FF',
    100: '#BEE3F8',
    200: '#90CDF4',
    300: '#63B3ED',
    400: '#4299E1',
    500: '#3182CE',
    600: '#2B77CB',
    700: '#2C5282',
    800: '#2A4365',
    900: '#1A365D',
  },
  // 辅助色 - 橙色系
  orange: {
    50: '#FFFAF0',
    100: '#FEEBC8',
    200: '#FBD38D',
    300: '#F6AD55',
    400: '#ED8936',
    500: '#DD6B20',
    600: '#C05621',
    700: '#9C4221',
    800: '#7B341E',
    900: '#652B19',
  },
  // 成功色 - 绿色系
  green: {
    50: '#F0FFF4',
    100: '#C6F6D5',
    200: '#9AE6B4',
    300: '#68D391',
    400: '#48BB78',
    500: '#38A169',
    600: '#2F855A',
    700: '#276749',
    800: '#22543D',
    900: '#1C4532',
  },
  // 警告色 - 黄色系
  yellow: {
    50: '#FFFFF0',
    100: '#FEFCBF',
    200: '#FAF089',
    300: '#F6E05E',
    400: '#ECC94B',
    500: '#D69E2E',
    600: '#B7791F',
    700: '#975A16',
    800: '#744210',
    900: '#5F370E',
  },
  // 错误色 - 红色系
  red: {
    50: '#FFF5F5',
    100: '#FED7D7',
    200: '#FEB2B2',
    300: '#FC8181',
    400: '#F56565',
    500: '#E53E3E',
    600: '#C53030',
    700: '#9B2C2C',
    800: '#822727',
    900: '#63171B',
  },
  // 中性色 - 灰色系
  gray: {
    50: '#F7FAFC',
    100: '#EDF2F7',
    200: '#E2E8F0',
    300: '#CBD5E0',
    400: '#A0AEC0',
    500: '#718096',
    600: '#4A5568',
    700: '#2D3748',
    800: '#1A202C',
    900: '#171923',
  },
  // 紫色系 - 用于特殊功能
  purple: {
    50: '#FAF5FF',
    100: '#E9D8FD',
    200: '#D6BCFA',
    300: '#B794F6',
    400: '#9F7AEA',
    500: '#805AD5',
    600: '#6B46C1',
    700: '#553C9A',
    800: '#44337A',
    900: '#322659',
  },
};

// 语义化颜色系统
export const lightColors = {
  // 主要颜色
  primary: {
    50: colorPalette.blue[50],
    100: colorPalette.blue[100],
    200: colorPalette.blue[200],
    300: colorPalette.blue[300],
    400: colorPalette.blue[400],
    500: colorPalette.blue[500],
    600: colorPalette.blue[600],
    700: colorPalette.blue[700],
    800: colorPalette.blue[800],
    900: colorPalette.blue[900],
  },
  // 辅助颜色
  secondary: {
    50: colorPalette.orange[50],
    100: colorPalette.orange[100],
    200: colorPalette.orange[200],
    300: colorPalette.orange[300],
    400: colorPalette.orange[400],
    500: colorPalette.orange[500],
    600: colorPalette.orange[600],
    700: colorPalette.orange[700],
    800: colorPalette.orange[800],
    900: colorPalette.orange[900],
  },
  // 状态颜色
  success: colorPalette.green,
  warning: colorPalette.yellow,
  error: colorPalette.red,
  info: colorPalette.blue,

  // 中性色
  gray: colorPalette.gray,

  // 语义化背景和文本
  background: {
    primary: '#FFFFFF',
    secondary: colorPalette.gray[50],
    tertiary: colorPalette.gray[100],
    inverse: colorPalette.gray[900],
  },
  surface: {
    primary: '#FFFFFF',
    secondary: colorPalette.gray[50],
    tertiary: colorPalette.gray[100],
    elevated: '#FFFFFF',
    overlay: 'rgba(0, 0, 0, 0.6)',
  },
  text: {
    primary: colorPalette.gray[900],
    secondary: colorPalette.gray[600],
    tertiary: colorPalette.gray[500],
    inverse: '#FFFFFF',
    disabled: colorPalette.gray[400],
  },
  border: {
    primary: colorPalette.gray[200],
    secondary: colorPalette.gray[300],
    focus: colorPalette.blue[500],
    error: colorPalette.red[500],
  },
  // 特殊用途颜色
  accent: colorPalette.purple[500],
  highlight: colorPalette.yellow[200],
  shadow: 'rgba(0, 0, 0, 0.1)',
};

export const darkColors = {
  // 主要颜色 (在深色模式下稍微调亮)
  primary: {
    50: colorPalette.blue[900],
    100: colorPalette.blue[800],
    200: colorPalette.blue[700],
    300: colorPalette.blue[600],
    400: colorPalette.blue[500],
    500: colorPalette.blue[400],
    600: colorPalette.blue[300],
    700: colorPalette.blue[200],
    800: colorPalette.blue[100],
    900: colorPalette.blue[50],
  },
  // 辅助颜色
  secondary: {
    50: colorPalette.orange[900],
    100: colorPalette.orange[800],
    200: colorPalette.orange[700],
    300: colorPalette.orange[600],
    400: colorPalette.orange[500],
    500: colorPalette.orange[400],
    600: colorPalette.orange[300],
    700: colorPalette.orange[200],
    800: colorPalette.orange[100],
    900: colorPalette.orange[50],
  },
  // 状态颜色 (深色模式优化)
  success: {
    ...colorPalette.green,
    500: '#48BB78', // 稍微调亮以提高对比度
  },
  warning: {
    ...colorPalette.yellow,
    500: '#ECC94B',
  },
  error: {
    ...colorPalette.red,
    500: '#F56565',
  },
  info: {
    ...colorPalette.blue,
    500: '#4299E1',
  },

  // 中性色 (反转)
  gray: {
    50: colorPalette.gray[900],
    100: colorPalette.gray[800],
    200: colorPalette.gray[700],
    300: colorPalette.gray[600],
    400: colorPalette.gray[500],
    500: colorPalette.gray[400],
    600: colorPalette.gray[300],
    700: colorPalette.gray[200],
    800: colorPalette.gray[100],
    900: colorPalette.gray[50],
  },

  // 语义化背景和文本 (深色模式)
  background: {
    primary: '#1A202C',
    secondary: '#2D3748',
    tertiary: '#4A5568',
    inverse: '#FFFFFF',
  },
  surface: {
    primary: '#1A202C',
    secondary: '#2D3748',
    tertiary: '#4A5568',
    elevated: '#2D3748',
    overlay: 'rgba(0, 0, 0, 0.8)',
  },
  text: {
    primary: '#FFFFFF',
    secondary: colorPalette.gray[300],
    tertiary: colorPalette.gray[400],
    inverse: colorPalette.gray[900],
    disabled: colorPalette.gray[500],
  },
  border: {
    primary: colorPalette.gray[600],
    secondary: colorPalette.gray[500],
    focus: colorPalette.blue[400],
    error: colorPalette.red[400],
  },
  // 特殊用途颜色
  accent: colorPalette.purple[400],
  highlight: colorPalette.yellow[800],
  shadow: 'rgba(0, 0, 0, 0.3)',
};

export type Colors = typeof lightColors; 