// 快速修复工具 - 用于解决当前编译错误的临时方案

// 1. 颜色引用快速修复映射
export const colorQuickFix = {
  // 将旧的颜色引用映射到简单的颜色值
  separator: '#E2E8F0',
  textSecondary: '#718096',
  red: '#E53E3E',
  green: '#38A169',
  blue: '#3182CE',
  surface: '#F7FAFC',
  tertiary: '#63B3ED',
};

// 2. 快速修复函数 - 用于组件中的颜色引用
export const getColor = (theme: any, colorPath: string): string => {
  // 处理新的嵌套颜色结构
  if (colorPath.includes('.')) {
    const keys = colorPath.split('.');
    let value = theme.colors;
    
    for (const key of keys) {
      value = value?.[key];
      if (!value) break;
    }
    
    if (value && typeof value === 'string') {
      return value;
    }
  }
  
  // 处理旧的颜色引用
  const oldColorMap: { [key: string]: string } = {
    separator: colorQuickFix.separator,
    textSecondary: colorQuickFix.textSecondary,
    red: colorQuickFix.red,
    green: colorQuickFix.green,
    blue: colorQuickFix.blue,
    surface: colorQuickFix.surface,
    tertiary: colorQuickFix.tertiary,
  };
  
  return oldColorMap[colorPath] || theme.colors.primary?.[500] || '#3182CE';
};

// 3. 间距快速修复
export const getSpacing = (theme: any, spacingKey: string): string => {
  const spacingMap: { [key: string]: string } = {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
    xxl: '48px',
    xxxl: '64px',
  };
  
  return theme.spacing?.[spacingKey] || spacingMap[spacingKey] || '16px';
};

// 4. 尺寸快速修复
export const getSize = (size: string): string => {
  const sizeMap: { [key: string]: string } = {
    small: 'sm',
    medium: 'md',
    large: 'lg',
  };
  
  return sizeMap[size] || size;
};

// 5. 响应式断点快速修复
export const breakpoints = {
  xs: '0px',
  sm: '576px',
  md: '768px',
  lg: '992px',
  xl: '1200px',
  xxl: '1400px',
};

export const mediaQuery = {
  up: (breakpoint: keyof typeof breakpoints) => 
    `@media (min-width: ${breakpoints[breakpoint]})`,
  down: (breakpoint: keyof typeof breakpoints) => 
    `@media (max-width: ${parseInt(breakpoints[breakpoint]) - 1}px)`,
};

// 6. 快速修复的全局样式
export const quickFixStyles = `
  /* 快速修复的全局样式 */
  .quick-fix-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
  }
  
  .quick-fix-grid {
    display: grid;
    gap: 16px;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
  
  .quick-fix-flex {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .quick-fix-button {
    padding: 12px 20px;
    border-radius: 8px;
    border: none;
    background-color: #3182CE;
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .quick-fix-button:hover {
    background-color: #2C5282;
  }
  
  .quick-fix-input {
    padding: 12px 16px;
    border: 1px solid #E2E8F0;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.2s ease;
  }
  
  .quick-fix-input:focus {
    outline: none;
    border-color: #3182CE;
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
  }
  
  .quick-fix-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #E2E8F0;
  }
  
  @media (max-width: 768px) {
    .quick-fix-container {
      padding: 0 12px;
    }
    
    .quick-fix-grid {
      grid-template-columns: 1fr;
      gap: 12px;
    }
    
    .quick-fix-card {
      padding: 16px;
    }
  }
`;

// 7. 快速修复的主题对象
export const quickFixTheme = {
  colors: {
    primary: '#3182CE',
    secondary: '#ED8936',
    success: '#38A169',
    warning: '#D69E2E',
    error: '#E53E3E',
    background: '#FFFFFF',
    surface: '#F7FAFC',
    text: '#1A202C',
    textSecondary: '#718096',
    border: '#E2E8F0',
    separator: '#E2E8F0',
  },
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
    xxl: '48px',
  },
  radius: {
    sm: '4px',
    md: '8px',
    lg: '12px',
    xl: '16px',
  },
  shadows: {
    sm: '0 1px 3px rgba(0, 0, 0, 0.1)',
    md: '0 4px 6px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px rgba(0, 0, 0, 0.1)',
  },
};
