import React, { ReactNode, ButtonHTMLAttributes } from 'react';
import styled, { css } from 'styled-components';
import { useTheme } from '../../../design-system/theme/ThemeProvider';
import { responsive, spacing, typography, layout } from '../../../design-system/utils/styleUtils';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'tertiary' | 'ghost' | 'danger';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  loading?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  children: ReactNode;
  onClick?: () => void;
}

// 按钮变体样式
const getVariantStyles = (variant: ButtonProps['variant']) => {
  switch (variant) {
    case 'primary':
      return css`
        background-color: ${({ theme }) => theme.colors.primary[500]};
        color: ${({ theme }) => theme.colors.background.primary};
        border: 1px solid ${({ theme }) => theme.colors.primary[500]};

        &:hover:not(:disabled) {
          background-color: ${({ theme }) => theme.colors.primary[600]};
          border-color: ${({ theme }) => theme.colors.primary[600]};
        }

        &:active:not(:disabled) {
          background-color: ${({ theme }) => theme.colors.primary[700]};
          border-color: ${({ theme }) => theme.colors.primary[700]};
        }

        &:focus-visible {
          box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.primary[200]};
        }
      `;

    case 'secondary':
      return css`
        background-color: ${({ theme }) => theme.colors.secondary[500]};
        color: ${({ theme }) => theme.colors.background.primary};
        border: 1px solid ${({ theme }) => theme.colors.secondary[500]};

        &:hover:not(:disabled) {
          background-color: ${({ theme }) => theme.colors.secondary[600]};
          border-color: ${({ theme }) => theme.colors.secondary[600]};
        }

        &:active:not(:disabled) {
          background-color: ${({ theme }) => theme.colors.secondary[700]};
          border-color: ${({ theme }) => theme.colors.secondary[700]};
        }

        &:focus-visible {
          box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.secondary[200]};
        }
      `;

    case 'tertiary':
      return css`
        background-color: ${({ theme }) => theme.colors.surface.secondary};
        color: ${({ theme }) => theme.colors.text.primary};
        border: 1px solid ${({ theme }) => theme.colors.border.primary};

        &:hover:not(:disabled) {
          background-color: ${({ theme }) => theme.colors.surface.tertiary};
          border-color: ${({ theme }) => theme.colors.border.secondary};
        }

        &:active:not(:disabled) {
          background-color: ${({ theme }) => theme.colors.gray[200]};
        }

        &:focus-visible {
          box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.primary[200]};
        }
      `;

    case 'ghost':
      return css`
        background-color: transparent;
        color: ${({ theme }) => theme.colors.primary[500]};
        border: 1px solid transparent;

        &:hover:not(:disabled) {
          background-color: ${({ theme }) => theme.colors.primary[50]};
          color: ${({ theme }) => theme.colors.primary[600]};
        }

        &:active:not(:disabled) {
          background-color: ${({ theme }) => theme.colors.primary[100]};
        }

        &:focus-visible {
          box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.primary[200]};
        }
      `;

    case 'danger':
      return css`
        background-color: ${({ theme }) => theme.colors.error[500]};
        color: ${({ theme }) => theme.colors.background.primary};
        border: 1px solid ${({ theme }) => theme.colors.error[500]};

        &:hover:not(:disabled) {
          background-color: ${({ theme }) => theme.colors.error[600]};
          border-color: ${({ theme }) => theme.colors.error[600]};
        }

        &:active:not(:disabled) {
          background-color: ${({ theme }) => theme.colors.error[700]};
          border-color: ${({ theme }) => theme.colors.error[700]};
        }

        &:focus-visible {
          box-shadow: 0 0 0 3px ${({ theme }) => theme.colors.error[200]};
        }
      `;

    default:
      return getVariantStyles('primary');
  }
};

// 按钮尺寸样式
const getSizeStyles = (size: ButtonProps['size']) => {
  switch (size) {
    case 'xs':
      return css`
        ${typography.apply('body.xs')}
        padding: ${({ theme }) => theme.spacing.component.button.sm};
        min-height: 28px;
      `;

    case 'sm':
      return css`
        ${typography.apply('body.sm')}
        padding: ${({ theme }) => theme.spacing.component.button.sm};
        min-height: 32px;
      `;

    case 'md':
      return css`
        ${typography.apply('body.base')}
        padding: ${({ theme }) => theme.spacing.component.button.md};
        min-height: 40px;
      `;

    case 'lg':
      return css`
        ${typography.apply('body.lg')}
        padding: ${({ theme }) => theme.spacing.component.button.lg};
        min-height: 48px;
      `;

    case 'xl':
      return css`
        ${typography.apply('body.xl')}
        padding: ${({ theme }) => theme.spacing[4]} ${({ theme }) => theme.spacing[8]};
        min-height: 56px;
      `;

    default:
      return getSizeStyles('md');
  }
};

const StyledButton = styled.button<ButtonProps>`
  /* 基础样式 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: ${({ theme }) => theme.spacing[2]};
  font-family: inherit;
  font-weight: ${({ theme }) => theme.typography.body.base.fontWeight};
  line-height: 1;
  text-decoration: none;
  white-space: nowrap;
  border-radius: ${({ theme }) => theme.radius.md};
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;

  /* 尺寸样式 */
  ${({ size }) => getSizeStyles(size)}

  /* 变体样式 */
  ${({ variant }) => getVariantStyles(variant)}

  /* 全宽样式 */
  ${({ fullWidth }) => fullWidth && css`
    width: 100%;
  `}

  /* 禁用状态 */
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  /* 加载状态 */
  ${({ loading }) => loading && css`
    color: transparent;
    pointer-events: none;
  `}

  /* 焦点样式 */
  &:focus {
    outline: none;
  }

  &:focus-visible {
    outline: 2px solid ${({ theme }) => theme.colors.primary[500]};
    outline-offset: 2px;
  }

  /* 活动状态 */
  &:active:not(:disabled) {
    transform: translateY(1px);
  }

  /* 响应式调整 */
  ${responsive.down('sm', css`
    ${({ fullWidth }) => !fullWidth && css`
      min-width: auto;
      padding-left: ${({ theme }) => theme.spacing[3]};
      padding-right: ${({ theme }) => theme.spacing[3]};
    `}
  `)}
`;

// 加载动画组件
const LoadingSpinner = styled.div<{ size?: ButtonProps['size'] }>`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  ${({ size }) => {
    switch (size) {
      case 'xs':
      case 'sm':
        return css`
          width: 14px;
          height: 14px;
          border-width: 2px;
        `;
      case 'lg':
      case 'xl':
        return css`
          width: 20px;
          height: 20px;
          border-width: 2px;
        `;
      default:
        return css`
          width: 16px;
          height: 16px;
          border-width: 2px;
        `;
    }
  }}

  border: solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: button-loading-spinner 1s linear infinite;

  @keyframes button-loading-spinner {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
  }
`;

// 图标容器
const IconContainer = styled.span<{ position: 'left' | 'right' }>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  ${({ position }) => position === 'left' && css`
    margin-right: ${({ theme }) => theme.spacing[1]};
  `}

  ${({ position }) => position === 'right' && css`
    margin-left: ${({ theme }) => theme.spacing[1]};
  `}
`;

export const Button = ({
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  fullWidth = false,
  leftIcon,
  rightIcon,
  children,
  onClick,
  type = 'button',
  ...props
}: ButtonProps) => {
  const { theme } = useTheme();

  return (
    <StyledButton
      variant={variant}
      size={size}
      loading={loading}
      disabled={disabled || loading}
      fullWidth={fullWidth}
      onClick={onClick}
      type={type}
      aria-disabled={disabled || loading}
      aria-busy={loading}
      {...props}
    >
      {leftIcon && !loading && (
        <IconContainer position="left">
          {leftIcon}
        </IconContainer>
      )}

      <span style={{ opacity: loading ? 0 : 1 }}>
        {children}
      </span>

      {rightIcon && !loading && (
        <IconContainer position="right">
          {rightIcon}
        </IconContainer>
      )}

      {loading && <LoadingSpinner size={size} />}
    </StyledButton>
  );
};