import React, { ReactNode, ButtonHTMLAttributes } from 'react';
import styled, { css } from 'styled-components';
import { useTheme } from '../../../design-system/theme/ThemeProvider';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  children: ReactNode;
  onClick?: () => void;
}

const StyledButton = styled.button<ButtonProps>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-family: inherit;
  font-weight: 500;
  line-height: 1;
  text-decoration: none;
  white-space: nowrap;
  border-radius: ${(props) => props.theme.radius.md};
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;

  /* 尺寸样式 */
  ${(props) => {
    switch (props.size) {
      case 'sm':
        return css`
          font-size: 14px;
          padding: 8px 16px;
          min-height: 32px;
        `;
      case 'lg':
        return css`
          font-size: 18px;
          padding: 16px 24px;
          min-height: 48px;
        `;
      default: // md
        return css`
          font-size: 16px;
          padding: 12px 20px;
          min-height: 40px;
        `;
    }
  }}

  /* 变体样式 */
  ${(props) => {
    switch (props.variant) {
      case 'secondary':
        return css`
          background-color: ${props.theme.colors.secondary};
          color: ${props.theme.colors.background};
          border: 1px solid ${props.theme.colors.secondary};

          &:hover:not(:disabled) {
            opacity: 0.8;
          }
        `;
      case 'ghost':
        return css`
          background-color: transparent;
          color: ${props.theme.colors.primary};
          border: 1px solid ${props.theme.colors.primary};

          &:hover:not(:disabled) {
            background-color: ${props.theme.colors.primary};
            color: ${props.theme.colors.background};
          }
        `;
      default: // primary
        return css`
          background-color: ${props.theme.colors.primary};
          color: ${props.theme.colors.background};
          border: 1px solid ${props.theme.colors.primary};

          &:hover:not(:disabled) {
            opacity: 0.8;
          }
        `;
    }
  }}

  /* 禁用状态 */
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  /* 加载状态 */
  ${({ loading }) => loading && css`
    color: transparent;
    pointer-events: none;
  `}

  /* 焦点样式 */
  &:focus {
    outline: none;
  }

  &:focus-visible {
    outline: 2px solid ${({ theme }) => theme.colors.primary};
    outline-offset: 2px;
  }

  /* 活动状态 */
  &:active:not(:disabled) {
    transform: translateY(1px);
  }
`;

// 加载动画组件
const LoadingSpinner = styled.div<{ size?: ButtonProps['size'] }>`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: button-loading-spinner 1s linear infinite;

  @keyframes button-loading-spinner {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
  }
`;

export const Button = ({
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  children,
  onClick,
  type = 'button',
  ...props
}: ButtonProps) => {
  const { theme } = useTheme();

  return (
    <StyledButton
      variant={variant}
      size={size}
      loading={loading}
      disabled={disabled || loading}
      onClick={onClick}
      type={type}
      aria-disabled={disabled || loading}
      aria-busy={loading}
      {...props}
    >
      <span style={{ opacity: loading ? 0 : 1 }}>
        {children}
      </span>

      {loading && <LoadingSpinner size={size} />}
    </StyledButton>
  );
};