import React, { ReactNode, ButtonHTMLAttributes } from 'react';
import styled from 'styled-components';
import { useTheme } from '../../../design-system/theme/ThemeProvider';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
  disabled?: boolean;
  children: ReactNode;
  onClick?: () => void;
}

const StyledButton = styled.button<ButtonProps>`
  padding: ${(props) => {
    switch (props.size) {
      case 'small':
        return props.theme.spacing.sm;
      case 'medium':
        return props.theme.spacing.md;
      case 'large':
        return props.theme.spacing.lg;
      default:
        return props.theme.spacing.md;
    }
  }};
  background-color: ${(props) => {
    switch (props.variant) {
      case 'primary':
        return props.theme.colors.primary;
      case 'secondary':
        return props.theme.colors.secondary;
      case 'ghost':
        return 'transparent';
      default:
        return props.theme.colors.primary;
    }
  }};
  color: ${(props) => (props.variant === 'ghost' ? props.theme.colors.text : props.theme.colors.background)};
  border: ${(props) => (props.variant === 'ghost' ? `1px solid ${props.theme.colors.primary}` : 'none')};
  border-radius: ${(props) => props.theme.radius.md};
  opacity: ${(props) => (props.disabled || props.loading ? 0.6 : 1)};
  cursor: ${(props) => (props.disabled || props.loading ? 'not-allowed' : 'pointer')};
  transition: all 0.2s ease;

  &:hover {
    opacity: ${(props) => (props.disabled || props.loading ? 0.6 : 0.8)};
  }
`;

const Spinner = styled.div`
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  width: 16px;
  height: 16px;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

export const Button = ({
  variant = 'primary',
  size = 'medium',
  loading = false,
  disabled = false,
  children,
  onClick,
  type = 'button',
  ...props
}: ButtonProps) => {
  const { theme } = useTheme();

  return (
    <StyledButton
      variant={variant}
      size={size}
      loading={loading}
      disabled={disabled || loading}
      onClick={onClick}
      type={type}
      {...props}
    >
      {loading ? <Spinner /> : children}
    </StyledButton>
  );
}; 