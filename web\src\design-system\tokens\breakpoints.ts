// 断点系统 - 基于常见设备尺寸
export const breakpoints = {
  // 基础断点值 (px)
  values: {
    xs: 0,      // 超小屏幕 (手机竖屏)
    sm: 576,    // 小屏幕 (手机横屏)
    md: 768,    // 中等屏幕 (平板竖屏)
    lg: 992,    // 大屏幕 (平板横屏/小桌面)
    xl: 1200,   // 超大屏幕 (桌面)
    xxl: 1400,  // 超超大屏幕 (大桌面)
  },
  
  // 媒体查询字符串
  up: {
    xs: '@media (min-width: 0px)',
    sm: '@media (min-width: 576px)',
    md: '@media (min-width: 768px)',
    lg: '@media (min-width: 992px)',
    xl: '@media (min-width: 1200px)',
    xxl: '@media (min-width: 1400px)',
  },
  
  down: {
    xs: '@media (max-width: 575.98px)',
    sm: '@media (max-width: 767.98px)',
    md: '@media (max-width: 991.98px)',
    lg: '@media (max-width: 1199.98px)',
    xl: '@media (max-width: 1399.98px)',
    xxl: '@media (max-width: 9999px)', // 实际上不会用到
  },
  
  between: {
    xsAndSm: '@media (min-width: 0px) and (max-width: 767.98px)',
    smAndMd: '@media (min-width: 576px) and (max-width: 991.98px)',
    mdAndLg: '@media (min-width: 768px) and (max-width: 1199.98px)',
    lgAndXl: '@media (min-width: 992px) and (max-width: 1399.98px)',
  },
  
  only: {
    xs: '@media (max-width: 575.98px)',
    sm: '@media (min-width: 576px) and (max-width: 767.98px)',
    md: '@media (min-width: 768px) and (max-width: 991.98px)',
    lg: '@media (min-width: 992px) and (max-width: 1199.98px)',
    xl: '@media (min-width: 1200px) and (max-width: 1399.98px)',
    xxl: '@media (min-width: 1400px)',
  },
};

// 容器最大宽度
export const containerMaxWidths = {
  sm: '540px',
  md: '720px',
  lg: '960px',
  xl: '1140px',
  xxl: '1320px',
};

// 栅格系统配置
export const grid = {
  columns: 12,
  gutterWidth: {
    xs: '16px',
    sm: '16px',
    md: '24px',
    lg: '24px',
    xl: '24px',
    xxl: '24px',
  },
  containerPadding: {
    xs: '16px',
    sm: '16px',
    md: '24px',
    lg: '24px',
    xl: '24px',
    xxl: '24px',
  },
};

// 工具函数
export const mediaQuery = {
  // 生成媒体查询
  up: (breakpoint: keyof typeof breakpoints.values) => 
    `@media (min-width: ${breakpoints.values[breakpoint]}px)`,
  
  down: (breakpoint: keyof typeof breakpoints.values) => {
    const value = breakpoints.values[breakpoint];
    return `@media (max-width: ${value - 0.02}px)`;
  },
  
  between: (start: keyof typeof breakpoints.values, end: keyof typeof breakpoints.values) =>
    `@media (min-width: ${breakpoints.values[start]}px) and (max-width: ${breakpoints.values[end] - 0.02}px)`,
  
  only: (breakpoint: keyof typeof breakpoints.values) => {
    const keys = Object.keys(breakpoints.values) as Array<keyof typeof breakpoints.values>;
    const index = keys.indexOf(breakpoint);
    
    if (index === 0) {
      return mediaQuery.down(keys[1]);
    } else if (index === keys.length - 1) {
      return mediaQuery.up(breakpoint);
    } else {
      return mediaQuery.between(breakpoint, keys[index + 1]);
    }
  },
};

export type Breakpoint = keyof typeof breakpoints.values;
export type BreakpointValues = typeof breakpoints.values;
