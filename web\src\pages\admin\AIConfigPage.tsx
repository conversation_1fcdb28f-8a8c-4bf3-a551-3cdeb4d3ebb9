import React, { useState, useEffect } from 'react';
import axios from 'axios';

interface AIConfig {
  id: string;
  name: string;
  provider: string;
  model_id: string;
  api_url: string;
  api_key_env_var: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

const AIConfigPage: React.FC = () => {
  const [configs, setConfigs] = useState<AIConfig[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState<boolean>(false);
  const [newConfig, setNewConfig] = useState<Partial<AIConfig>>({
    name: '',
    provider: '',
    model_id: '',
    api_url: '',
    api_key_env_var: '',
    is_active: true,
  });
  const [editingConfig, setEditingConfig] = useState<AIConfig | null>(null);

  /**
   * 获取所有AI配置
   */
  const fetchAIConfigs = async () => {
    try {
      setLoading(true);
       const response = await axios.get<AIConfig[]>('/api/configs/ai');
      setConfigs(response.data);
    } catch (err) {
      setError('Failed to fetch AI configurations.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 处理新增配置表单提交
   */
  const handleAddSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
       const response = await axios.post('/api/configs/ai', newConfig);
      setShowAddForm(false);
      setNewConfig({
        name: '',
        provider: '',
        model_id: '',
        api_url: '',
        api_key_env_var: '',
        is_active: true,
      });
      fetchAIConfigs(); // Refresh the list
    } catch (err) {
      setError('Failed to add AI configuration.');
      console.error(err);
    }
  };

  /**
   * 处理编辑配置表单提交
   */
  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingConfig) return;
    try {
       const response = await axios.put(`/api/configs/ai/${editingConfig.id}`, editingConfig);
      setEditingConfig(null);
      fetchAIConfigs(); // Refresh the list
    } catch (err) {
      setError('Failed to update AI configuration.');
      console.error(err);
    }
  };

  /**
   * 处理删除配置
   */
  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this configuration?')) {
      try {
         await axios.delete(`/api/configs/ai/${id}`);
        fetchAIConfigs(); // Refresh the list
      } catch (err) {
        setError('Failed to delete AI configuration.');
        console.error(err);
      }
    }
  };

  useEffect(() => {
    fetchAIConfigs();
  }, []);

  if (loading) {
    return <div>Loading AI Configurations...</div>;
  }

  if (error) {
    return <div style={{ color: 'red' }}>Error: {error}</div>;
  }

  return (
    <div>
      <h1>AI Configuration Management</h1>

      <button onClick={() => { setShowAddForm(true); setEditingConfig(null); }}>Add New Config</button>

      {showAddForm && (
        <form onSubmit={handleAddSubmit}>
          <h2>Add New AI Configuration</h2>
          <div>
            <label>Name:</label>
            <input
              type="text"
              value={newConfig.name}
              onChange={(e) => setNewConfig({ ...newConfig, name: e.target.value })}
              required
            />
          </div>
          <div>
            <label>Provider:</label>
            <input
              type="text"
              value={newConfig.provider}
              onChange={(e) => setNewConfig({ ...newConfig, provider: e.target.value })}
              required
            />
          </div>
          <div>
            <label>Model ID:</label>
            <input
              type="text"
              value={newConfig.model_id}
              onChange={(e) => setNewConfig({ ...newConfig, model_id: e.target.value })}
              required
            />
          </div>
          <div>
            <label>API URL:</label>
            <input
              type="text"
              value={newConfig.api_url}
              onChange={(e) => setNewConfig({ ...newConfig, api_url: e.target.value })}
              required
            />
          </div>
          <div>
            <label>API Key Env Var:</label>
            <input
              type="text"
              value={newConfig.api_key_env_var}
              onChange={(e) => setNewConfig({ ...newConfig, api_key_env_var: e.target.value })}
              required
            />
          </div>
          <div>
            <label>Is Active:</label>
            <input
              type="checkbox"
              checked={newConfig.is_active}
              onChange={(e) => setNewConfig({ ...newConfig, is_active: e.target.checked })}
            />
          </div>
          <button type="submit">Add Config</button>
          <button type="button" onClick={() => setShowAddForm(false)}>Cancel</button>
        </form>
      )}

      {editingConfig && (
        <form onSubmit={handleEditSubmit}>
          <h2>Edit AI Configuration</h2>
          <div>
            <label>Name:</label>
            <input
              type="text"
              value={editingConfig.name}
              onChange={(e) => setEditingConfig({ ...editingConfig, name: e.target.value })}
              required
            />
          </div>
          <div>
            <label>Provider:</label>
            <input
              type="text"
              value={editingConfig.provider}
              onChange={(e) => setEditingConfig({ ...editingConfig, provider: e.target.value })}
              required
            />
          </div>
          <div>
            <label>Model ID:</label>
            <input
              type="text"
              value={editingConfig.model_id}
              onChange={(e) => setEditingConfig({ ...editingConfig, model_id: e.target.value })}
              required
            />
          </div>
          <div>
            <label>API URL:</label>
            <input
              type="text"
              value={editingConfig.api_url}
              onChange={(e) => setEditingConfig({ ...editingConfig, api_url: e.target.value })}
              required
            />
          </div>
          <div>
            <label>API Key Env Var:</label>
            <input
              type="text"
              value={editingConfig.api_key_env_var}
              onChange={(e) => setEditingConfig({ ...editingConfig, api_key_env_var: e.target.value })}
              required
            />
          </div>
          <div>
            <label>Is Active:</label>
            <input
              type="checkbox"
              checked={editingConfig.is_active}
              onChange={(e) => setEditingConfig({ ...editingConfig, is_active: e.target.checked })}
            />
          </div>
          <button type="submit">Update Config</button>
          <button type="button" onClick={() => setEditingConfig(null)}>Cancel</button>
        </form>
      )}

      <table>
        <thead>
          <tr>
            <th>ID</th>
            <th>Model ID</th>
            <th>API URL</th>
            <th>API Key Env</th>
            <th>Enabled</th>
            <th>Created At</th>
            <th>Updated At</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {configs.map((config) => (
            <tr key={config.id}>
              <td>{config.id}</td>
              <td>{config.model_id}</td>
              <td>{config.api_url}</td>
              <td>{config.api_key_env_var}</td>
              <td>{config.is_active ? 'Yes' : 'No'}</td>
              <td>{new Date(config.created_at).toLocaleString()}</td>
              <td>{new Date(config.updated_at).toLocaleString()}</td>
              <td>
                <button onClick={() => { setEditingConfig(config); setShowAddForm(false); }}>Edit</button>
                <button onClick={() => handleDelete(config.id)}>Delete</button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default AIConfigPage;