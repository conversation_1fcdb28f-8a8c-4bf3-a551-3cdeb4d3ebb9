import { css } from 'styled-components';

// 响应式工具函数
export const responsive = {
  // 根据断点应用不同的样式
  up: (breakpoint: string, styles: any) => css`
    ${({ theme }) => theme.mediaQuery.up(breakpoint)} {
      ${styles}
    }
  `,

  down: (breakpoint: string, styles: any) => css`
    ${({ theme }) => theme.mediaQuery.down(breakpoint)} {
      ${styles}
    }
  `,

  between: (start: string, end: string, styles: any) => css`
    ${({ theme }) => theme.mediaQuery.between(start, end)} {
      ${styles}
    }
  `,

  only: (breakpoint: string, styles: any) => css`
    ${({ theme }) => theme.mediaQuery.only(breakpoint)} {
      ${styles}
    }
  `,
};

// 间距工具函数
export const spacing = {
  // 生成响应式间距
  responsive: (mobile: string, tablet?: string, desktop?: string) => css`
    margin: ${mobile};

    ${responsive.up('md', css`margin: ${tablet || mobile};`)}
    ${responsive.up('lg', css`margin: ${desktop || tablet || mobile};`)}
  `,

  // 快捷间距函数
  margin: (value: string | number) => css`
    margin: ${typeof value === 'number' ? `${value}px` : value};
  `,

  padding: (value: string | number) => css`
    padding: ${typeof value === 'number' ? `${value}px` : value};
  `,

  // 方向性间距
  marginX: (value: string | number) => css`
    margin-left: ${typeof value === 'number' ? `${value}px` : value};
    margin-right: ${typeof value === 'number' ? `${value}px` : value};
  `,

  marginY: (value: string | number) => css`
    margin-top: ${typeof value === 'number' ? `${value}px` : value};
    margin-bottom: ${typeof value === 'number' ? `${value}px` : value};
  `,

  paddingX: (value: string | number) => css`
    padding-left: ${typeof value === 'number' ? `${value}px` : value};
    padding-right: ${typeof value === 'number' ? `${value}px` : value};
  `,

  paddingY: (value: string | number) => css`
    padding-top: ${typeof value === 'number' ? `${value}px` : value};
    padding-bottom: ${typeof value === 'number' ? `${value}px` : value};
  `,
};

// 排版工具函数
export const typography = {
  // 应用排版样式
  apply: (variant: string) => css`
    ${({ theme }) => {
      const style = theme.typography[variant];
      if (!style) return '';

      return css`
        font-family: ${style.fontFamily};
        font-size: ${typeof style.fontSize === 'object' ? style.fontSize.base : style.fontSize};
        font-weight: ${style.fontWeight};
        line-height: ${style.lineHeight};
        letter-spacing: ${style.letterSpacing};

        ${typeof style.fontSize === 'object' && style.fontSize.md && responsive.up('md', css`
          font-size: ${style.fontSize.md};
        `)}

        ${typeof style.fontSize === 'object' && style.fontSize.lg && responsive.up('lg', css`
          font-size: ${style.fontSize.lg};
        `)}
      `;
    }}
  `,

  // 文本截断
  truncate: css`
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  `,

  // 多行文本截断
  lineClamp: (lines: number) => css`
    display: -webkit-box;
    -webkit-line-clamp: ${lines};
    -webkit-box-orient: vertical;
    overflow: hidden;
  `,
};

// 布局工具函数
export const layout = {
  // 容器样式
  container: css`
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: ${({ theme }) => theme.grid.containerPadding.xs};
    padding-right: ${({ theme }) => theme.grid.containerPadding.xs};

    ${responsive.up('sm', css`
      max-width: ${({ theme }) => theme.containerMaxWidths.sm};
      padding-left: ${({ theme }) => theme.grid.containerPadding.sm};
      padding-right: ${({ theme }) => theme.grid.containerPadding.sm};
    `)}

    ${responsive.up('md', css`
      max-width: ${({ theme }) => theme.containerMaxWidths.md};
      padding-left: ${({ theme }) => theme.grid.containerPadding.md};
      padding-right: ${({ theme }) => theme.grid.containerPadding.md};
    `)}

    ${responsive.up('lg', css`
      max-width: ${({ theme }) => theme.containerMaxWidths.lg};
      padding-left: ${({ theme }) => theme.grid.containerPadding.lg};
      padding-right: ${({ theme }) => theme.grid.containerPadding.lg};
    `)}

    ${responsive.up('xl', css`
      max-width: ${({ theme }) => theme.containerMaxWidths.xl};
      padding-left: ${({ theme }) => theme.grid.containerPadding.xl};
      padding-right: ${({ theme }) => theme.grid.containerPadding.xl};
    `)}

    ${responsive.up('xxl', css`
      max-width: ${({ theme }) => theme.containerMaxWidths.xxl};
      padding-left: ${({ theme }) => theme.grid.containerPadding.xxl};
      padding-right: ${({ theme }) => theme.grid.containerPadding.xxl};
    `)}
  `,

  // Flexbox 工具
  flex: {
    center: css`
      display: flex;
      align-items: center;
      justify-content: center;
    `,

    between: css`
      display: flex;
      align-items: center;
      justify-content: space-between;
    `,

    column: css`
      display: flex;
      flex-direction: column;
    `,

    wrap: css`
      display: flex;
      flex-wrap: wrap;
    `,
  },

  // Grid 工具
  grid: {
    container: css`
      display: grid;
      gap: ${({ theme }) => theme.grid.gutterWidth.xs};

      ${responsive.up('sm', css`gap: ${({ theme }) => theme.grid.gutterWidth.sm};`)}
      ${responsive.up('md', css`gap: ${({ theme }) => theme.grid.gutterWidth.md};`)}
      ${responsive.up('lg', css`gap: ${({ theme }) => theme.grid.gutterWidth.lg};`)}
      ${responsive.up('xl', css`gap: ${({ theme }) => theme.grid.gutterWidth.xl};`)}
      ${responsive.up('xxl', css`gap: ${({ theme }) => theme.grid.gutterWidth.xxl};`)}
    `,

    columns: (cols: number | { [key: string]: number }) => css`
      ${typeof cols === 'number'
        ? `grid-template-columns: repeat(${cols}, 1fr);`
        : Object.entries(cols).map(([breakpoint, colCount]) =>
            responsive.up(breakpoint, css`
              grid-template-columns: repeat(${colCount}, 1fr);
            `)
          )
      }
    `,
  },
};

// 向后兼容的旧版本断点系统
export const breakpoints = {
  mobile: '375px',
  tablet: '768px',
  desktop: '1024px',
};

export const media = {
  mobile: (...args: Parameters<typeof css>) => css`
    @media (max-width: ${breakpoints.mobile}) {
      ${css(...args)}
    }
  `,
  tablet: (...args: Parameters<typeof css>) => css`
    @media (max-width: ${breakpoints.tablet}) {
      ${css(...args)}
    }
  `,
  desktop: (...args: Parameters<typeof css>) => css`
    @media (min-width: ${breakpoints.desktop}) {
      ${css(...args)}
    }
  `,
};