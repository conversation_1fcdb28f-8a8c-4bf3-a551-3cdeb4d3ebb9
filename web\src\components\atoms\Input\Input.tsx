import React, { InputHTMLAttributes, ReactNode, TextareaHTMLAttributes } from 'react';
import styled, { DefaultTheme } from 'styled-components';
import { useTheme } from 'design-system/theme/ThemeProvider';

// Define base props common to both input and textarea
interface CommonProps {
  inputPrefix?: ReactNode;
  inputSuffix?: ReactNode;
  error?: boolean;
}

// Discriminated union for InputProps
type InputProps = CommonProps & (
  | ({ as?: 'input' } & InputHTMLAttributes<HTMLInputElement>)
  | ({ as: 'textarea' } & TextareaHTMLAttributes<HTMLTextAreaElement>)
);

interface InputWrapperProps {
  error: boolean;
  theme: DefaultTheme;
}

const InputWrapper = styled.div<InputWrapperProps>`
  display: flex;
  align-items: center;
  border: 1px solid ${(props) => (props.theme.colors.separator)};
  border-radius: ${(props) => props.theme.radius.md};
  padding: ${(props) => props.theme.spacing.sm};
  background-color: ${(props) => props.theme.colors.surface};
  transition: border-color 0.2s ease;

  ${(props) =>
    props.error &&
    `
    border-color: ${props.theme.colors.red};
  `}

  &:focus-within {
    border-color: ${(props) => props.theme.colors.primary};
  }
`;

// StyledInput will receive all props from Input, including `as`
const StyledInput = styled.input<InputProps & { theme: DefaultTheme }>`
  flex-grow: 1;
  border: none;
  outline: none;
  background-color: transparent;
  color: ${(props) => props.theme.colors.text};
  font-size: ${(props) => props.theme.typography.body.fontSize};
  padding: 0;

  &::placeholder {
    color: ${(props) => props.theme.colors.textSecondary};
  }
  ${(props) => props.as === 'textarea' && `
    resize: vertical;
  `}
`;

interface PrefixSuffixContainerProps {
  theme: DefaultTheme;
}

const PrefixSuffixContainer = styled.div<PrefixSuffixContainerProps>`
  display: flex;
  align-items: center;
  color: ${(props) => props.theme.colors.textSecondary};
  padding: 0 ${(props) => props.theme.spacing.xs};
`;

export const Input = ({
  inputPrefix,
  inputSuffix,
  error = false,
  as = 'input',
  ...props
}: InputProps) => {
  const { theme } = useTheme();

  return (
    <InputWrapper theme={theme} error={error}>
      {inputPrefix && <PrefixSuffixContainer theme={theme}>{inputPrefix}</PrefixSuffixContainer>}
      <StyledInput as={as} theme={theme} {...props} />
      {inputSuffix && <PrefixSuffixContainer theme={theme}>{inputSuffix}</PrefixSuffixContainer>}
    </InputWrapper>
  );
}; 