// 基础间距系统 - 基于8px网格系统
export const spacing = {
  // 基础间距值
  0: '0px',
  1: '4px',    // 0.25rem
  2: '8px',    // 0.5rem
  3: '12px',   // 0.75rem
  4: '16px',   // 1rem
  5: '20px',   // 1.25rem
  6: '24px',   // 1.5rem
  8: '32px',   // 2rem
  10: '40px',  // 2.5rem
  12: '48px',  // 3rem
  16: '64px',  // 4rem
  20: '80px',  // 5rem
  24: '96px',  // 6rem
  32: '128px', // 8rem
  40: '160px', // 10rem
  48: '192px', // 12rem
  56: '224px', // 14rem
  64: '256px', // 16rem

  // 语义化命名 (向后兼容)
  none: '0px',
  xxs: '2px',
  xs: '4px',
  sm: '8px',
  md: '16px',
  lg: '24px',
  xl: '32px',
  xxl: '48px',
  xxxl: '64px',

  // 组件特定间距
  component: {
    // 按钮内边距
    button: {
      sm: '8px 12px',
      md: '12px 16px',
      lg: '16px 24px',
    },
    // 输入框内边距
    input: {
      sm: '8px 12px',
      md: '12px 16px',
      lg: '16px 20px',
    },
    // 卡片内边距
    card: {
      sm: '16px',
      md: '24px',
      lg: '32px',
    },
    // 容器间距
    container: {
      sm: '16px',
      md: '24px',
      lg: '32px',
      xl: '48px',
    },
  },

  // 布局间距
  layout: {
    // 页面边距
    page: {
      mobile: '16px',
      tablet: '24px',
      desktop: '32px',
    },
    // 栅格间距
    grid: {
      xs: '8px',
      sm: '16px',
      md: '24px',
      lg: '32px',
    },
    // 节间距
    section: {
      sm: '32px',
      md: '48px',
      lg: '64px',
      xl: '96px',
    },
  },
};

// 响应式间距工具函数
const responsiveHelper = (mobile: string, tablet?: string, desktop?: string) => ({
  mobile: mobile,
  tablet: tablet || mobile,
  desktop: desktop || tablet || mobile,
});

export const responsiveSpacing = {
  // 根据屏幕尺寸返回不同的间距值
  responsive: responsiveHelper,

  // 快捷响应式间距
  xs: responsiveHelper(spacing[2], spacing[3], spacing[4]),
  sm: responsiveHelper(spacing[4], spacing[6], spacing[8]),
  md: responsiveHelper(spacing[6], spacing[8], spacing[10]),
  lg: responsiveHelper(spacing[8], spacing[12], spacing[16]),
  xl: responsiveHelper(spacing[12], spacing[16], spacing[20]),
};

export type Spacing = typeof spacing;