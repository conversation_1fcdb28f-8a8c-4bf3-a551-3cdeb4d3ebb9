import React, { useState } from 'react';
import axios from 'axios';
import { AuthLayout } from '../../layouts/AuthLayout';
import { Button, Input, Typography } from '../../components/atoms';
import { Card, FormField } from '../../components/molecules';
import { LoadingSpinner } from '../../components/animations/LoadingSpinner';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { useTheme } from 'design-system/theme/ThemeProvider';

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${(props) => props.theme.spacing.md};
  margin-top: ${(props) => props.theme.spacing.lg};
`;

const PageTitle = styled(Typography)`
  text-align: center;
`;

const Message = styled(Typography)`
  margin-top: ${(props) => props.theme.spacing.sm};
  text-align: center;
`;

const Register: React.FC = () => {
  const { theme } = useTheme();
  const [account, setAccount] = useState('');
  const [password, setPassword] = useState('');
  const [confirm, setConfirm] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    setLoading(true);
    if (password !== confirm) {
      setError('兩次密碼不一致');
      setLoading(false);
      return;
    }
    try {
      await axios.post('/api/user/register', { account, password });
      setSuccess('註冊成功，請登錄');
    } catch (err: any) {
      setError(err.response?.data?.message || '註冊失敗');
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthLayout>
      <Card>
        <PageTitle variant="title1">用戶註冊</PageTitle>
        <Form onSubmit={handleSubmit} theme={theme}>
          <FormField label="賬號">
            <Input
              type="text"
              placeholder="請輸入賬號"
              value={account}
              onChange={(e) => setAccount(e.target.value)}
              required
            />
          </FormField>
          <FormField label="密碼">
            <Input
              type="password"
              placeholder="請輸入密碼"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </FormField>
          <FormField label="確認密碼">
            <Input
              type="password"
              placeholder="請再次輸入密碼"
              value={confirm}
              onChange={(e) => setConfirm(e.target.value)}
              required
            />
          </FormField>
          <Button
            variant="primary"
            size="large"
            loading={loading}
            disabled={loading}
            type="submit"
          >
            {loading ? <LoadingSpinner /> : '註冊'}
          </Button>
        </Form>
        {error && <Message variant="body" color={theme.colors.red}>{error}</Message>}
        {success && <Message variant="body" color={theme.colors.green}>{success}</Message>}
      </Card>
    </AuthLayout>
  );
};

export default Register; 