import { createGlobalStyle } from 'styled-components';

export const GlobalStyles = createGlobalStyle`
  /* CSS Reset - 现代化重置样式 */
  *,
  *::before,
  *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  /* 根元素设置 */
  html {
    font-size: 16px; /* 基础字体大小 */
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    scroll-behavior: smooth;
  }

  /* 身体元素 */
  body {
    font-family: ${({ theme }) => theme.typography.body.base.fontFamily};
    font-size: ${({ theme }) => theme.typography.body.base.fontSize};
    font-weight: ${({ theme }) => theme.typography.body.base.fontWeight};
    line-height: ${({ theme }) => theme.typography.body.base.lineHeight};
    color: ${({ theme }) => theme.colors.text.primary};
    background-color: ${({ theme }) => theme.colors.background.primary};
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    overflow-x: hidden;
  }

  /* 标题元素 */
  h1, h2, h3, h4, h5, h6 {
    font-weight: ${({ theme }) => theme.typography.heading.lg.fontWeight};
    line-height: ${({ theme }) => theme.typography.heading.lg.lineHeight};
    color: ${({ theme }) => theme.colors.text.primary};
    margin-bottom: ${({ theme }) => theme.spacing[4]};
  }

  h1 {
    font-size: ${({ theme }) => theme.typography.heading['2xl'].fontSize.base};
    ${({ theme }) => theme.mediaQuery.up('md')} {
      font-size: ${({ theme }) => theme.typography.heading['2xl'].fontSize.md};
    }
  }

  h2 {
    font-size: ${({ theme }) => theme.typography.heading.xl.fontSize.base};
    ${({ theme }) => theme.mediaQuery.up('md')} {
      font-size: ${({ theme }) => theme.typography.heading.xl.fontSize.md};
    }
  }

  h3 {
    font-size: ${({ theme }) => theme.typography.heading.lg.fontSize.base};
    ${({ theme }) => theme.mediaQuery.up('md')} {
      font-size: ${({ theme }) => theme.typography.heading.lg.fontSize.md};
    }
  }

  h4 {
    font-size: ${({ theme }) => theme.typography.heading.md.fontSize};
  }

  h5 {
    font-size: ${({ theme }) => theme.typography.heading.sm.fontSize};
  }

  h6 {
    font-size: ${({ theme }) => theme.typography.heading.sm.fontSize};
    font-weight: ${({ theme }) => theme.typography.heading.sm.fontWeight};
  }

  /* 段落和文本 */
  p {
    margin-bottom: ${({ theme }) => theme.spacing[4]};
    color: ${({ theme }) => theme.colors.text.primary};
  }

  /* 链接 */
  a {
    color: ${({ theme }) => theme.colors.primary[500]};
    text-decoration: none;
    transition: color 0.2s ease;

    &:hover {
      color: ${({ theme }) => theme.colors.primary[600]};
      text-decoration: underline;
    }

    &:focus {
      outline: 2px solid ${({ theme }) => theme.colors.primary[500]};
      outline-offset: 2px;
    }
  }

  /* 按钮重置 */
  button {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    color: inherit;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
    margin: 0;

    &:focus {
      outline: 2px solid ${({ theme }) => theme.colors.primary[500]};
      outline-offset: 2px;
    }
  }

  /* 输入框重置 */
  input,
  textarea,
  select {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    color: inherit;
    background: transparent;
    border: none;
    padding: 0;
    margin: 0;

    &:focus {
      outline: 2px solid ${({ theme }) => theme.colors.primary[500]};
      outline-offset: 2px;
    }
  }

  /* 列表 */
  ul, ol {
    padding-left: ${({ theme }) => theme.spacing[6]};
    margin-bottom: ${({ theme }) => theme.spacing[4]};
  }

  li {
    margin-bottom: ${({ theme }) => theme.spacing[1]};
  }

  /* 代码 */
  code {
    font-family: ${({ theme }) => theme.typography.code.base.fontFamily};
    font-size: ${({ theme }) => theme.typography.code.base.fontSize};
    background-color: ${({ theme }) => theme.colors.surface.secondary};
    padding: ${({ theme }) => theme.spacing[1]} ${({ theme }) => theme.spacing[2]};
    border-radius: ${({ theme }) => theme.radius.sm};
    color: ${({ theme }) => theme.colors.text.primary};
  }

  pre {
    font-family: ${({ theme }) => theme.typography.code.base.fontFamily};
    background-color: ${({ theme }) => theme.colors.surface.secondary};
    padding: ${({ theme }) => theme.spacing[4]};
    border-radius: ${({ theme }) => theme.radius.md};
    overflow-x: auto;
    margin-bottom: ${({ theme }) => theme.spacing[4]};

    code {
      background: transparent;
      padding: 0;
    }
  }

  /* 图片 */
  img {
    max-width: 100%;
    height: auto;
    display: block;
  }

  /* 表格 */
  table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: ${({ theme }) => theme.spacing[4]};
  }

  th, td {
    padding: ${({ theme }) => theme.spacing[3]};
    text-align: left;
    border-bottom: 1px solid ${({ theme }) => theme.colors.border.primary};
  }

  th {
    font-weight: ${({ theme }) => theme.typography.heading.sm.fontWeight};
    color: ${({ theme }) => theme.colors.text.primary};
    background-color: ${({ theme }) => theme.colors.surface.secondary};
  }

  /* 水平分割线 */
  hr {
    border: none;
    height: 1px;
    background-color: ${({ theme }) => theme.colors.border.primary};
    margin: ${({ theme }) => theme.spacing[8]} 0;
  }

  /* 引用 */
  blockquote {
    border-left: 4px solid ${({ theme }) => theme.colors.primary[500]};
    padding-left: ${({ theme }) => theme.spacing[4]};
    margin: ${({ theme }) => theme.spacing[4]} 0;
    font-style: italic;
    color: ${({ theme }) => theme.colors.text.secondary};
  }

  /* 选择文本样式 */
  ::selection {
    background-color: ${({ theme }) => theme.colors.primary[100]};
    color: ${({ theme }) => theme.colors.primary[900]};
  }

  /* 滚动条样式 (Webkit) */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: ${({ theme }) => theme.colors.surface.secondary};
  }

  ::-webkit-scrollbar-thumb {
    background: ${({ theme }) => theme.colors.gray[400]};
    border-radius: ${({ theme }) => theme.radius.full};

    &:hover {
      background: ${({ theme }) => theme.colors.gray[500]};
    }
  }

  /* 焦点可见性改进 */
  .js-focus-visible :focus:not(.focus-visible) {
    outline: none;
  }

  /* 减少动画对于偏好减少动画的用户 */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }

  /* 高对比度模式支持 */
  @media (prefers-contrast: high) {
    button,
    input,
    select,
    textarea {
      border: 1px solid;
    }
  }

  /* 打印样式 */
  @media print {
    *,
    *::before,
    *::after {
      background: transparent !important;
      color: black !important;
      box-shadow: none !important;
      text-shadow: none !important;
    }

    a,
    a:visited {
      text-decoration: underline;
    }

    a[href]::after {
      content: " (" attr(href) ")";
    }

    abbr[title]::after {
      content: " (" attr(title) ")";
    }

    pre {
      white-space: pre-wrap !important;
    }

    pre,
    blockquote {
      border: 1px solid #999;
      page-break-inside: avoid;
    }

    thead {
      display: table-header-group;
    }

    tr,
    img {
      page-break-inside: avoid;
    }

    p,
    h2,
    h3 {
      orphans: 3;
      widows: 3;
    }

    h2,
    h3 {
      page-break-after: avoid;
    }
  }
`;
