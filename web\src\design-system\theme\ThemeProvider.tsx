import React, { createContext, useState, useContext, ReactNode } from 'react';
import { ThemeProvider as StyledThemeProvider } from 'styled-components';
import * as ColorsTokens from '../tokens/colors';
import * as TypographyTokens from '../tokens/typography';
import * as SpacingTokens from '../tokens/spacing';
import * as ShadowsTokens from '../tokens/shadows';
import * as RadiusTokens from '../tokens/radius';
import * as BreakpointsTokens from '../tokens/breakpoints';

interface Theme {
  colors: typeof ColorsTokens.lightColors;
  typography: TypographyTokens.Typography;
  spacing: SpacingTokens.Spacing;
  shadows: ShadowsTokens.Shadows;
  radius: RadiusTokens.Radius;
  breakpoints: BreakpointsTokens.BreakpointValues;
  mediaQuery: typeof BreakpointsTokens.mediaQuery;
  containerMaxWidths: typeof BreakpointsTokens.containerMaxWidths;
  grid: typeof BreakpointsTokens.grid;
}

interface ThemeContextType {
  theme: Theme;
  themeMode: 'light' | 'dark';
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider = ({ children }: ThemeProviderProps) => {
  const [themeMode, setThemeMode] = useState<'light' | 'dark'>('light');

  const theme: Theme = {
    colors: themeMode === 'light' ? ColorsTokens.lightColors : ColorsTokens.darkColors,
    typography: TypographyTokens.typography,
    spacing: SpacingTokens.spacing,
    shadows: ShadowsTokens.shadows,
    radius: RadiusTokens.radius,
    breakpoints: BreakpointsTokens.breakpoints.values,
    mediaQuery: BreakpointsTokens.mediaQuery,
    containerMaxWidths: BreakpointsTokens.containerMaxWidths,
    grid: BreakpointsTokens.grid,
  };

  const toggleTheme = () => {
    setThemeMode((prevMode) => (prevMode === 'light' ? 'dark' : 'light'));
  };

  return (
    <ThemeContext.Provider value={{ theme, themeMode, toggleTheme }}>
      <StyledThemeProvider theme={theme}>
        {children}
      </StyledThemeProvider>
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}; 