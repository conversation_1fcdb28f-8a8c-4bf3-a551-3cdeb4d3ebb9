// 字体家族定义
export const fontFamilies = {
  // 系统字体栈 - 优先使用系统原生字体
  system: [
    '-apple-system',
    'BlinkMacSystemFont',
    '"Segoe UI"',
    'Roboto',
    '"Helvetica Neue"',
    'Arial',
    '"Noto Sans"',
    'sans-serif',
    '"Apple Color Emoji"',
    '"Segoe UI Emoji"',
    '"Segoe UI Symbol"',
    '"Noto Color Emoji"'
  ].join(', '),

  // 等宽字体
  mono: [
    '"SF Mono"',
    'Monaco',
    '"Cascadia Code"',
    '"Roboto Mono"',
    'Consolas',
    '"Liberation Mono"',
    '"Courier New"',
    'monospace'
  ].join(', '),

  // 中文字体优化
  chinese: [
    '-apple-system',
    'BlinkMacSystemFont',
    '"Segoe UI"',
    '"PingFang SC"',
    '"Hiragino Sans GB"',
    '"Microsoft YaHei"',
    '"Helvetica Neue"',
    'Helvetica',
    'Arial',
    'sans-serif'
  ].join(', '),
};

// 字重定义
export const fontWeights = {
  thin: 100,
  extraLight: 200,
  light: 300,
  normal: 400,
  medium: 500,
  semiBold: 600,
  bold: 700,
  extraBold: 800,
  black: 900,
};

// 字号系统 - 基于模块化比例 (1.25 - 大三度)
export const fontSizes = {
  xs: '0.75rem',    // 12px
  sm: '0.875rem',   // 14px
  base: '1rem',     // 16px
  lg: '1.125rem',   // 18px
  xl: '1.25rem',    // 20px
  '2xl': '1.5rem',  // 24px
  '3xl': '1.875rem', // 30px
  '4xl': '2.25rem',  // 36px
  '5xl': '3rem',     // 48px
  '6xl': '3.75rem',  // 60px
  '7xl': '4.5rem',   // 72px
  '8xl': '6rem',     // 96px
  '9xl': '8rem',     // 128px
};

// 行高系统
export const lineHeights = {
  none: 1,
  tight: 1.25,
  snug: 1.375,
  normal: 1.5,
  relaxed: 1.625,
  loose: 2,
  // 数值行高
  3: '0.75rem',
  4: '1rem',
  5: '1.25rem',
  6: '1.5rem',
  7: '1.75rem',
  8: '2rem',
  9: '2.25rem',
  10: '2.5rem',
};

// 字间距系统
export const letterSpacings = {
  tighter: '-0.05em',
  tight: '-0.025em',
  normal: '0em',
  wide: '0.025em',
  wider: '0.05em',
  widest: '0.1em',
};

// 响应式排版系统
export const typography = {
  // 显示级别 - 用于大标题
  display: {
    '2xl': {
      fontSize: { base: fontSizes['5xl'], md: fontSizes['6xl'], lg: fontSizes['7xl'] },
      fontWeight: fontWeights.bold,
      lineHeight: lineHeights.none,
      letterSpacing: letterSpacings.tight,
      fontFamily: fontFamilies.system,
    },
    xl: {
      fontSize: { base: fontSizes['4xl'], md: fontSizes['5xl'], lg: fontSizes['6xl'] },
      fontWeight: fontWeights.bold,
      lineHeight: lineHeights.tight,
      letterSpacing: letterSpacings.tight,
      fontFamily: fontFamilies.system,
    },
    lg: {
      fontSize: { base: fontSizes['3xl'], md: fontSizes['4xl'], lg: fontSizes['5xl'] },
      fontWeight: fontWeights.bold,
      lineHeight: lineHeights.tight,
      letterSpacing: letterSpacings.tight,
      fontFamily: fontFamilies.system,
    },
  },

  // 标题级别
  heading: {
    '2xl': {
      fontSize: { base: fontSizes['2xl'], md: fontSizes['3xl'] },
      fontWeight: fontWeights.bold,
      lineHeight: lineHeights.tight,
      letterSpacing: letterSpacings.tight,
      fontFamily: fontFamilies.system,
    },
    xl: {
      fontSize: { base: fontSizes.xl, md: fontSizes['2xl'] },
      fontWeight: fontWeights.semiBold,
      lineHeight: lineHeights.tight,
      letterSpacing: letterSpacings.tight,
      fontFamily: fontFamilies.system,
    },
    lg: {
      fontSize: { base: fontSizes.lg, md: fontSizes.xl },
      fontWeight: fontWeights.semiBold,
      lineHeight: lineHeights.tight,
      letterSpacing: letterSpacings.normal,
      fontFamily: fontFamilies.system,
    },
    md: {
      fontSize: fontSizes.base,
      fontWeight: fontWeights.semiBold,
      lineHeight: lineHeights.snug,
      letterSpacing: letterSpacings.normal,
      fontFamily: fontFamilies.system,
    },
    sm: {
      fontSize: fontSizes.sm,
      fontWeight: fontWeights.semiBold,
      lineHeight: lineHeights.snug,
      letterSpacing: letterSpacings.normal,
      fontFamily: fontFamilies.system,
    },
  },

  // 正文级别
  body: {
    xl: {
      fontSize: fontSizes.xl,
      fontWeight: fontWeights.normal,
      lineHeight: lineHeights.relaxed,
      letterSpacing: letterSpacings.normal,
      fontFamily: fontFamilies.system,
    },
    lg: {
      fontSize: fontSizes.lg,
      fontWeight: fontWeights.normal,
      lineHeight: lineHeights.relaxed,
      letterSpacing: letterSpacings.normal,
      fontFamily: fontFamilies.system,
    },
    base: {
      fontSize: fontSizes.base,
      fontWeight: fontWeights.normal,
      lineHeight: lineHeights.normal,
      letterSpacing: letterSpacings.normal,
      fontFamily: fontFamilies.system,
    },
    sm: {
      fontSize: fontSizes.sm,
      fontWeight: fontWeights.normal,
      lineHeight: lineHeights.normal,
      letterSpacing: letterSpacings.normal,
      fontFamily: fontFamilies.system,
    },
    xs: {
      fontSize: fontSizes.xs,
      fontWeight: fontWeights.normal,
      lineHeight: lineHeights.normal,
      letterSpacing: letterSpacings.wide,
      fontFamily: fontFamilies.system,
    },
  },

  // 特殊用途
  label: {
    lg: {
      fontSize: fontSizes.sm,
      fontWeight: fontWeights.medium,
      lineHeight: lineHeights.snug,
      letterSpacing: letterSpacings.normal,
      fontFamily: fontFamilies.system,
    },
    base: {
      fontSize: fontSizes.xs,
      fontWeight: fontWeights.medium,
      lineHeight: lineHeights.snug,
      letterSpacing: letterSpacings.wide,
      fontFamily: fontFamilies.system,
    },
  },

  // 代码
  code: {
    base: {
      fontSize: fontSizes.sm,
      fontWeight: fontWeights.normal,
      lineHeight: lineHeights.normal,
      letterSpacing: letterSpacings.normal,
      fontFamily: fontFamilies.mono,
    },
  },

  // 向后兼容的旧版本样式
  largeTitle: {
    fontSize: fontSizes['4xl'],
    fontWeight: fontWeights.bold,
    lineHeight: lineHeights.tight,
    letterSpacing: letterSpacings.tight,
    fontFamily: fontFamilies.system,
  },
  title1: {
    fontSize: fontSizes['3xl'],
    fontWeight: fontWeights.semiBold,
    lineHeight: lineHeights.tight,
    letterSpacing: letterSpacings.tight,
    fontFamily: fontFamilies.system,
  },
  title2: {
    fontSize: fontSizes['2xl'],
    fontWeight: fontWeights.semiBold,
    lineHeight: lineHeights.tight,
    letterSpacing: letterSpacings.normal,
    fontFamily: fontFamilies.system,
  },
  title3: {
    fontSize: fontSizes.xl,
    fontWeight: fontWeights.semiBold,
    lineHeight: lineHeights.snug,
    letterSpacing: letterSpacings.normal,
    fontFamily: fontFamilies.system,
  },
  headline: {
    fontSize: fontSizes.lg,
    fontWeight: fontWeights.semiBold,
    lineHeight: lineHeights.snug,
    letterSpacing: letterSpacings.normal,
    fontFamily: fontFamilies.system,
  },
  bodyText: {
    fontSize: fontSizes.base,
    fontWeight: fontWeights.normal,
    lineHeight: lineHeights.normal,
    letterSpacing: letterSpacings.normal,
    fontFamily: fontFamilies.system,
  },
  callout: {
    fontSize: fontSizes.base,
    fontWeight: fontWeights.normal,
    lineHeight: lineHeights.normal,
    letterSpacing: letterSpacings.normal,
    fontFamily: fontFamilies.system,
  },
  subhead: {
    fontSize: fontSizes.sm,
    fontWeight: fontWeights.normal,
    lineHeight: lineHeights.normal,
    letterSpacing: letterSpacings.normal,
    fontFamily: fontFamilies.system,
  },
  footnote: {
    fontSize: fontSizes.xs,
    fontWeight: fontWeights.normal,
    lineHeight: lineHeights.normal,
    letterSpacing: letterSpacings.wide,
    fontFamily: fontFamilies.system,
  },
  caption1: {
    fontSize: fontSizes.xs,
    fontWeight: fontWeights.normal,
    lineHeight: lineHeights.snug,
    letterSpacing: letterSpacings.wide,
    fontFamily: fontFamilies.system,
  },
  caption2: {
    fontSize: fontSizes.xs,
    fontWeight: fontWeights.normal,
    lineHeight: lineHeights.tight,
    letterSpacing: letterSpacings.wider,
    fontFamily: fontFamilies.system,
  },
};

export type Typography = typeof typography;