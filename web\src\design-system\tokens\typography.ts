export const typography = {
  largeTitle: {
    fontSize: '34px',
    fontWeight: '700',
    lineHeight: '41px',
    letterSpacing: '-0.41px',
  },
  title1: {
    fontSize: '28px',
    fontWeight: '600',
    lineHeight: '34px',
    letterSpacing: '-0.35px',
  },
  title2: {
    fontSize: '22px',
    fontWeight: '600',
    lineHeight: '28px',
    letterSpacing: '-0.26px',
  },
  title3: {
    fontSize: '20px',
    fontWeight: '600',
    lineHeight: '25px',
    letterSpacing: '-0.24px',
  },
  headline: {
    fontSize: '17px',
    fontWeight: '600',
    lineHeight: '22px',
    letterSpacing: '-0.41px',
  },
  body: {
    fontSize: '17px',
    fontWeight: '400',
    lineHeight: '22px',
    letterSpacing: '-0.41px',
  },
  callout: {
    fontSize: '16px',
    fontWeight: '400',
    lineHeight: '21px',
    letterSpacing: '-0.32px',
  },
  subhead: {
    fontSize: '15px',
    fontWeight: '400',
    lineHeight: '20px',
    letterSpacing: '-0.24px',
  },
  footnote: {
    fontSize: '13px',
    fontWeight: '400',
    lineHeight: '18px',
    letterSpacing: '-0.08px',
  },
  caption1: {
    fontSize: '12px',
    fontWeight: '400',
    lineHeight: '16px',
    letterSpacing: '-0.24px',
  },
  caption2: {
    fontSize: '11px',
    fontWeight: '400',
    lineHeight: '13px',
    letterSpacing: '0.07px',
  },
};

export type Typography = typeof typography; 