import React, { ReactNode } from 'react';
import styled, { DefaultTheme } from 'styled-components';
import { useTheme } from 'design-system/theme/ThemeProvider';
import { motion, HTMLMotionProps } from 'framer-motion';

interface CardProps extends HTMLMotionProps<'div'> {
  children: ReactNode;
}

interface StyledCardProps extends HTMLMotionProps<'div'> {
  theme: DefaultTheme;
}

const StyledCard = styled(motion.div)<StyledCardProps>`
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: ${(props) => props.theme.radius.lg};
  box-shadow: ${(props) => props.theme.shadows.sm};
  padding: ${(props) => props.theme.spacing.lg};
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid ${(props) => props.theme.colors.separator};

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${(props) => props.theme.shadows.md};
  }
`;

export const Card = ({ children, ...props }: CardProps) => {
  const { theme } = useTheme();

  return (
    <StyledCard theme={theme} {...props}>
      {children}
    </StyledCard>
  );
}; 